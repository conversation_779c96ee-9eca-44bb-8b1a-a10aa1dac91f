/**
 * مكون إعدادات التاريخ والوقت والمنطقة الزمنية
 * يتيح للمستخدم التحكم في تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';
import { FaClock, FaGlobe, FaCalendarAlt, FaCheck, FaSpinner, FaSync, FaMapMarkerAlt, FaWifi } from 'react-icons/fa';
import { SelectInput } from './inputs';
import { getCurrentTripoliDateTime } from '../services/dateTimeService';
import { timezoneDetectionService, TimezoneInfo } from '../services/timezoneDetectionService';

interface DateTimeSettingsProps {
  settings: Record<string, string>;
  onSettingChange: (key: string, value: string) => void;
}

interface DateTimeFormatOption {
  value: string;
  label: string;
  example: string;
}

interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

const DateTimeSettings: React.FC<DateTimeSettingsProps> = ({
  settings,
  onSettingChange
}) => {

  const [previewDate, setPreviewDate] = useState<Date>(new Date());
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectedTimezone, setDetectedTimezone] = useState<TimezoneInfo | null>(null);
  const [showDetectionResult, setShowDetectionResult] = useState(false);

  // خيارات تنسيق التاريخ
  const dateFormatOptions: DateTimeFormatOption[] = [
    { value: 'dd/MM/yyyy', label: 'يوم/شهر/سنة', example: '15/07/2025' },
    { value: 'MM/dd/yyyy', label: 'شهر/يوم/سنة', example: '07/15/2025' },
    { value: 'yyyy-MM-dd', label: 'سنة-شهر-يوم', example: '2025-07-15' },
    { value: 'dd-MM-yyyy', label: 'يوم-شهر-سنة', example: '15-07-2025' },
    { value: 'dd.MM.yyyy', label: 'يوم.شهر.سنة', example: '15.07.2025' },
    { value: 'arabic', label: 'التاريخ العربي', example: '15 يوليو 2025' },
    { value: 'english', label: 'التاريخ الإنجليزي', example: 'July 15, 2025' }
  ];

  // خيارات تنسيق الوقت
  const timeFormatOptions: DateTimeFormatOption[] = [
    { value: '24h', label: '24 ساعة', example: '14:30' },
    { value: '12h', label: '12 ساعة', example: '2:30 PM' },
    { value: '12h_ar', label: '12 ساعة عربي', example: '2:30 م' }
  ];

  // خيارات المناطق الزمنية (مرتبة حسب الاستخدام في التطبيقات الشهيرة)
  const timezoneOptions: TimezoneOption[] = [
    // المناطق العربية والشرق الأوسط
    { value: 'Africa/Tripoli', label: 'طرابلس، ليبيا', offset: 'UTC+2' },
    { value: 'Africa/Cairo', label: 'القاهرة، مصر', offset: 'UTC+2' },
    { value: 'Asia/Riyadh', label: 'الرياض، السعودية', offset: 'UTC+3' },
    { value: 'Asia/Dubai', label: 'دبي، الإمارات', offset: 'UTC+4' },
    { value: 'Asia/Kuwait', label: 'الكويت', offset: 'UTC+3' },
    { value: 'Asia/Qatar', label: 'الدوحة، قطر', offset: 'UTC+3' },
    { value: 'Asia/Bahrain', label: 'المنامة، البحرين', offset: 'UTC+3' },
    { value: 'Asia/Baghdad', label: 'بغداد، العراق', offset: 'UTC+3' },
    { value: 'Asia/Damascus', label: 'دمشق، سوريا', offset: 'UTC+3' },
    { value: 'Asia/Amman', label: 'عمان، الأردن', offset: 'UTC+3' },
    { value: 'Asia/Beirut', label: 'بيروت، لبنان', offset: 'UTC+3' },
    { value: 'Africa/Tunis', label: 'تونس', offset: 'UTC+1' },
    { value: 'Africa/Algiers', label: 'الجزائر', offset: 'UTC+1' },
    { value: 'Africa/Casablanca', label: 'الدار البيضاء، المغرب', offset: 'UTC+1' },

    // أمريكا الشمالية (Google, Facebook, Microsoft, Apple)
    { value: 'America/New_York', label: 'نيويورك، أمريكا الشرقية', offset: 'UTC-5' },
    { value: 'America/Los_Angeles', label: 'لوس أنجلوس، أمريكا الغربية', offset: 'UTC-8' },
    { value: 'America/Chicago', label: 'شيكاغو، أمريكا الوسطى', offset: 'UTC-6' },
    { value: 'America/Denver', label: 'دنفر، أمريكا الجبلية', offset: 'UTC-7' },
    { value: 'America/Toronto', label: 'تورونتو، كندا', offset: 'UTC-5' },
    { value: 'America/Vancouver', label: 'فانكوفر، كندا', offset: 'UTC-8' },

    // أوروبا (SAP, Spotify, Skype)
    { value: 'Europe/London', label: 'لندن، بريطانيا', offset: 'UTC+0' },
    { value: 'Europe/Paris', label: 'باريس، فرنسا', offset: 'UTC+1' },
    { value: 'Europe/Berlin', label: 'برلين، ألمانيا', offset: 'UTC+1' },
    { value: 'Europe/Rome', label: 'روما، إيطاليا', offset: 'UTC+1' },
    { value: 'Europe/Madrid', label: 'مدريد، إسبانيا', offset: 'UTC+1' },
    { value: 'Europe/Amsterdam', label: 'أمستردام، هولندا', offset: 'UTC+1' },
    { value: 'Europe/Stockholm', label: 'ستوكهولم، السويد', offset: 'UTC+1' },
    { value: 'Europe/Zurich', label: 'زيورخ، سويسرا', offset: 'UTC+1' },
    { value: 'Europe/Moscow', label: 'موسكو، روسيا', offset: 'UTC+3' },

    // آسيا والمحيط الهادئ (Alibaba, Tencent, Sony, Samsung)
    { value: 'Asia/Tokyo', label: 'طوكيو، اليابان', offset: 'UTC+9' },
    { value: 'Asia/Seoul', label: 'سيول، كوريا الجنوبية', offset: 'UTC+9' },
    { value: 'Asia/Shanghai', label: 'شنغهاي، الصين', offset: 'UTC+8' },
    { value: 'Asia/Hong_Kong', label: 'هونغ كونغ', offset: 'UTC+8' },
    { value: 'Asia/Singapore', label: 'سنغافورة', offset: 'UTC+8' },
    { value: 'Asia/Kolkata', label: 'كولكاتا، الهند', offset: 'UTC+5:30' },
    { value: 'Asia/Mumbai', label: 'مومباي، الهند', offset: 'UTC+5:30' },
    { value: 'Asia/Bangkok', label: 'بانكوك، تايلاند', offset: 'UTC+7' },
    { value: 'Asia/Jakarta', label: 'جاكرتا، إندونيسيا', offset: 'UTC+7' },
    { value: 'Asia/Manila', label: 'مانيلا، الفلبين', offset: 'UTC+8' },
    { value: 'Australia/Sydney', label: 'سيدني، أستراليا', offset: 'UTC+10' },
    { value: 'Australia/Melbourne', label: 'ملبورن، أستراليا', offset: 'UTC+10' },
    { value: 'Pacific/Auckland', label: 'أوكلاند، نيوزيلندا', offset: 'UTC+12' },

    // أمريكا الجنوبية (MercadoLibre, Globo)
    { value: 'America/Sao_Paulo', label: 'ساو باولو، البرازيل', offset: 'UTC-3' },
    { value: 'America/Buenos_Aires', label: 'بوينس آيرس، الأرجنتين', offset: 'UTC-3' },
    { value: 'America/Santiago', label: 'سانتياغو، تشيلي', offset: 'UTC-4' },
    { value: 'America/Lima', label: 'ليما، بيرو', offset: 'UTC-5' },
    { value: 'America/Bogota', label: 'بوغوتا، كولومبيا', offset: 'UTC-5' },
    { value: 'America/Mexico_City', label: 'مكسيكو سيتي، المكسيك', offset: 'UTC-6' },

    // أفريقيا
    { value: 'Africa/Johannesburg', label: 'جوهانسبرغ، جنوب أفريقيا', offset: 'UTC+2' },
    { value: 'Africa/Lagos', label: 'لاغوس، نيجيريا', offset: 'UTC+1' },
    { value: 'Africa/Nairobi', label: 'نيروبي، كينيا', offset: 'UTC+3' },

    // مناطق خاصة
    { value: 'UTC', label: 'التوقيت العالمي المنسق (UTC)', offset: 'UTC+0' },
    { value: 'GMT', label: 'توقيت غرينتش (GMT)', offset: 'UTC+0' }
  ];

  // تحديث معاينة التاريخ كل ثانية
  useEffect(() => {
    const interval = setInterval(() => {
      setPreviewDate(getCurrentTripoliDateTime());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // وظيفة الكشف التلقائي عن المنطقة الزمنية
  const handleAutoDetectTimezone = async () => {
    setIsDetecting(true);
    setShowDetectionResult(false);

    try {
      const detected = await timezoneDetectionService.comprehensiveTimezoneDetection();
      setDetectedTimezone(detected);
      setShowDetectionResult(true);

      // تطبيق المنطقة الزمنية المكتشفة تلقائياً إذا كان الإعداد مفعلاً
      if (settings.auto_detect_timezone === 'true') {
        onSettingChange('timezone', detected.timezone);
      }
    } catch (error) {
      console.error('Error detecting timezone:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  // وظيفة تطبيق المنطقة الزمنية المكتشفة
  const applyDetectedTimezone = () => {
    if (detectedTimezone) {
      onSettingChange('timezone', detectedTimezone.timezone);
      setShowDetectionResult(false);
    }
  };

  // الحصول على القيم الحالية من الإعدادات
  const currentDateFormat = settings.date_format || 'dd/MM/yyyy';
  const currentTimeFormat = settings.time_format || '24h';
  const currentTimezone = settings.timezone || 'Africa/Tripoli';

  // تحويل التاريخ إلى المنطقة الزمنية المحددة
  const convertToSelectedTimezone = (date: Date): Date => {
    try {
      if (currentTimezone === 'Africa/Tripoli' || !currentTimezone) {
        return getCurrentTripoliDateTime();
      }

      // استخدام Intl.DateTimeFormat للتحويل الدقيق
      const formatter = new Intl.DateTimeFormat('en-CA', {
        timeZone: currentTimezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });

      const parts = formatter.formatToParts(date);
      const partsObj = parts.reduce((acc, part) => {
        acc[part.type] = part.value;
        return acc;
      }, {} as Record<string, string>);

      return new Date(
        parseInt(partsObj.year),
        parseInt(partsObj.month) - 1,
        parseInt(partsObj.day),
        parseInt(partsObj.hour),
        parseInt(partsObj.minute),
        parseInt(partsObj.second)
      );
    } catch (error) {
      console.error('Error converting to timezone:', error);
      return getCurrentTripoliDateTime();
    }
  };

  // تنسيق التاريخ للمعاينة مع دقة عالية
  const formatPreviewDate = (date: Date): string => {
    try {
      const convertedDate = convertToSelectedTimezone(date);
      const day = convertedDate.getDate().toString().padStart(2, '0');
      const month = (convertedDate.getMonth() + 1).toString().padStart(2, '0');
      const year = convertedDate.getFullYear().toString();

      const separator = settings.date_separator || '/';

      switch (currentDateFormat) {
        case 'dd/MM/yyyy':
          return `${day}${separator}${month}${separator}${year}`;
        case 'MM/dd/yyyy':
          return `${month}${separator}${day}${separator}${year}`;
        case 'yyyy-MM-dd':
          return `${year}${separator}${month}${separator}${day}`;
        case 'dd-MM-yyyy':
          return `${day}${separator}${month}${separator}${year}`;
        case 'dd.MM.yyyy':
          return `${day}${separator}${month}${separator}${year}`;
        case 'arabic':
          const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];
          return `${parseInt(day)} ${monthNames[convertedDate.getMonth()]} ${year}`;
        default:
          return `${day}${separator}${month}${separator}${year}`;
      }
    } catch (error) {
      console.error('Error formatting preview date:', error);
      return new Date().toLocaleDateString('ar-LY');
    }
  };

  // تنسيق الوقت للمعاينة مع دقة عالية
  const formatPreviewTime = (date: Date): string => {
    try {
      const convertedDate = convertToSelectedTimezone(date);
      const hours = convertedDate.getHours();
      const minutes = convertedDate.getMinutes();
      const seconds = convertedDate.getSeconds();

      const timeSeparator = settings.time_separator || ':';
      const showSeconds = settings.show_seconds === 'true';

      switch (currentTimeFormat) {
        case '12h':
          const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          const ampm = hours >= 12 ? 'PM' : 'AM';
          let time12 = `${hour12}${timeSeparator}${minutes.toString().padStart(2, '0')}`;
          if (showSeconds) {
            time12 += `${timeSeparator}${seconds.toString().padStart(2, '0')}`;
          }
          return `${time12} ${ampm}`;

        case '12h_ar':
          const hour12Ar = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          const ampmAr = hours >= 12 ? 'م' : 'ص';
          let time12Ar = `${hour12Ar}${timeSeparator}${minutes.toString().padStart(2, '0')}`;
          if (showSeconds) {
            time12Ar += `${timeSeparator}${seconds.toString().padStart(2, '0')}`;
          }
          return `${time12Ar} ${ampmAr}`;

        default: // 24h
          let time24 = `${hours.toString().padStart(2, '0')}${timeSeparator}${minutes.toString().padStart(2, '0')}`;
          if (showSeconds) {
            time24 += `${timeSeparator}${seconds.toString().padStart(2, '0')}`;
          }
          return time24;
      }
    } catch (error) {
      console.error('Error formatting preview time:', error);
      return new Date().toLocaleTimeString();
    }
  };

  // تنسيق المنطقة الزمنية للمعاينة
  const formatTimezoneInfo = (): string => {
    try {
      const selectedTimezone = timezoneOptions.find(tz => tz.value === currentTimezone);
      if (selectedTimezone) {
        return `${selectedTimezone.label} (${selectedTimezone.offset})`;
      }
      return currentTimezone;
    } catch (error) {
      console.error('Error formatting timezone info:', error);
      return 'Africa/Tripoli (UTC+2)';
    }
  };

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex items-center space-x-3 space-x-reverse mb-6">
        <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
          <FaClock className="text-primary-600 dark:text-primary-400 text-xl" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إعدادات التاريخ والوقت
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            تخصيص تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
          </p>
        </div>
      </div>

      {/* معاينة مباشرة محسنة */}
      <div className="bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-50 dark:from-primary-900/20 dark:via-blue-900/20 dark:to-indigo-900/20 p-6 rounded-2xl border border-primary-200 dark:border-primary-700 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/50 rounded-lg">
              <FaCalendarAlt className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                المعاينة المباشرة
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                يتم التحديث كل ثانية
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-green-600 dark:text-green-400 font-medium">مباشر</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* التاريخ */}
          <div className="bg-white dark:bg-gray-800/50 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 space-x-reverse mb-2">
              <FaCalendarAlt className="text-blue-500 text-sm" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">التاريخ</span>
            </div>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {formatPreviewDate(previewDate)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {currentDateFormat}
            </div>
          </div>

          {/* الوقت */}
          <div className="bg-white dark:bg-gray-800/50 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 space-x-reverse mb-2">
              <FaClock className="text-green-500 text-sm" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">الوقت</span>
            </div>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {formatPreviewTime(previewDate)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {currentTimeFormat}
            </div>
          </div>

          {/* المنطقة الزمنية */}
          <div className="bg-white dark:bg-gray-800/50 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 space-x-reverse mb-2">
              <FaGlobe className="text-purple-500 text-sm" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">المنطقة الزمنية</span>
            </div>
            <div className="text-sm font-bold text-gray-900 dark:text-gray-100">
              {formatTimezoneInfo()}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {currentTimezone}
            </div>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-blue-700 dark:text-blue-300">
                <strong>الفاصل:</strong> {settings.date_separator || '/'} | {settings.time_separator || ':'}
              </span>
            </div>
            <div className="text-blue-600 dark:text-blue-400">
              {settings.show_seconds === 'true' ? 'مع الثواني' : 'بدون ثواني'}
            </div>
          </div>
        </div>
      </div>

      {/* إعدادات التنسيق الأساسية */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <FaCalendarAlt className="text-blue-600 dark:text-blue-400 text-lg" />
          </div>
          <div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              إعدادات التنسيق الأساسية
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              اختر تنسيق عرض التاريخ والوقت
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* تنسيق التاريخ */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <FaCalendarAlt className="text-blue-500" />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تنسيق التاريخ
              </label>
            </div>
            <SelectInput
              name="date_format"
              value={currentDateFormat}
              onChange={(value) => onSettingChange('date_format', value)}
              options={dateFormatOptions.map(option => ({
                value: option.value,
                label: `${option.label} (${option.example})`,
                icon: <FaCalendarAlt className="text-primary-500" />
              }))}
              className="w-full"
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded">
              <strong>مثال:</strong> {dateFormatOptions.find(opt => opt.value === currentDateFormat)?.example || 'غير محدد'}
            </div>
          </div>

          {/* تنسيق الوقت */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <FaClock className="text-green-500" />
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تنسيق الوقت
              </label>
            </div>
            <SelectInput
              name="time_format"
              value={currentTimeFormat}
              onChange={(value) => onSettingChange('time_format', value)}
              options={timeFormatOptions.map(option => ({
                value: option.value,
                label: `${option.label} (${option.example})`,
                icon: <FaClock className="text-primary-500" />
              }))}
              className="w-full"
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded">
              <strong>مثال:</strong> {timeFormatOptions.find(opt => opt.value === currentTimeFormat)?.example || 'غير محدد'}
            </div>
          </div>
        </div>

        {/* إعدادات إضافية */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h5 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">إعدادات إضافية</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* إظهار الثواني */}
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  إظهار الثواني
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  في عرض الوقت
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.show_seconds === 'true'}
                  onChange={(e) => onSettingChange('show_seconds', e.target.checked.toString())}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* فاصل التاريخ */}
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-2">
                فاصل التاريخ
              </label>
              <select
                value={settings.date_separator || '/'}
                onChange={(e) => onSettingChange('date_separator', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="/">/</option>
                <option value="-">-</option>
                <option value=".">.</option>
                <option value=" ">مسافة</option>
              </select>
            </div>

            {/* فاصل الوقت */}
            <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-2">
                فاصل الوقت
              </label>
              <select
                value={settings.time_separator || ':'}
                onChange={(e) => onSettingChange('time_separator', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value=":">:</option>
                <option value=".">.</option>
                <option value="-">-</option>
                <option value=" ">مسافة</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* المنطقة الزمنية */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-gray-700 dark:text-gray-200 font-medium text-lg">
            المنطقة الزمنية
          </label>
          <button
            type="button"
            onClick={handleAutoDetectTimezone}
            disabled={isDetecting}
            className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDetecting ? (
              <FaSpinner className="animate-spin" />
            ) : (
              <FaSync />
            )}
            <span>{isDetecting ? 'جارٍ الكشف...' : 'كشف تلقائي'}</span>
          </button>
        </div>

        <SelectInput
          name="timezone"
          value={currentTimezone}
          onChange={(value) => onSettingChange('timezone', value)}
          options={timezoneOptions.map(option => ({
            value: option.value,
            label: `${option.label} (${option.offset})`,
            icon: <FaGlobe className="text-primary-500" />
          }))}
          icon={<FaGlobe />}
          className="w-full"
          searchable={true}
        />

        {/* نتيجة الكشف التلقائي */}
        {showDetectionResult && detectedTimezone && (
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-xl border border-green-200 dark:border-green-700">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 space-x-reverse">
                <FaMapMarkerAlt className="text-green-600 dark:text-green-400 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-1">
                    تم اكتشاف منطقتك الزمنية
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    <strong>{detectedTimezone.city}, {detectedTimezone.country}</strong>
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    {detectedTimezone.timezone} ({detectedTimezone.offsetString})
                    {detectedTimezone.isDST && ' - التوقيت الصيفي نشط'}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={applyDetectedTimezone}
                  className="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  تطبيق
                </button>
                <button
                  type="button"
                  onClick={() => setShowDetectionResult(false)}
                  className="px-3 py-1 text-xs bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}
      </div>



      {/* إعدادات التحديث التلقائي */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <FaWifi className="text-blue-500 mr-2" />
          إعدادات التحديث التلقائي
        </h4>

        <div className="space-y-4">
          {/* الكشف التلقائي عن المنطقة الزمنية */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                الكشف التلقائي عن المنطقة الزمنية
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                كشف المنطقة الزمنية تلقائياً عند تحميل الصفحة
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.auto_detect_timezone === 'true'}
                onChange={(e) => onSettingChange('auto_detect_timezone', e.target.checked.toString())}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          {/* تحديث الوقت من الإنترنت */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تحديث الوقت من الإنترنت
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                مزامنة الوقت مع خوادم الإنترنت للحصول على دقة أعلى
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.sync_internet_time === 'true'}
                onChange={(e) => onSettingChange('sync_internet_time', e.target.checked.toString())}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-700">
        <div className="flex items-start space-x-3 space-x-reverse">
          <FaCheck className="text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0" />
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="font-medium mb-1">ملاحظات مهمة:</p>
            <ul className="space-y-1 text-xs">
              <li>• سيتم تطبيق التغييرات على جميع أجزاء التطبيق</li>
              <li>• المنطقة الزمنية الافتراضية هي طرابلس، ليبيا (UTC+2)</li>
              <li>• يمكن معاينة التغييرات قبل الحفظ</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateTimeSettings;
