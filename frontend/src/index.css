/* Almarai Font - Local Files */
@font-face {
  font-family: 'Almarai';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('/fonts/almarai/almarai-300.ttf') format('truetype');
}

@font-face {
  font-family: 'Almarai';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/almarai/almarai-400.ttf') format('truetype');
}

@font-face {
  font-family: 'Almarai';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('/fonts/almarai/almarai-700.ttf') format('truetype');
}

@font-face {
  font-family: 'Almarai';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('/fonts/almarai/almarai-800.ttf') format('truetype');
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f9fafb;
    --color-text-primary: #1f2937;
    --color-text-secondary: #4b5563;
    --color-border: #e5e7eb;
    --color-card-bg: #ffffff;
    --color-card-shadow: rgba(0, 0, 0, 0.05);
    --color-input-bg: #ffffff;
    --color-input-border: #d1d5db;
    --color-header-bg: #ffffff;

    /* Toast notification variables for light mode */
    --toast-bg: #ffffff;
    --toast-color: #1f2937;
    --toast-border: #e5e7eb;
  }

  .dark {
    --color-bg-primary: #111827;
    --color-bg-secondary: #1f2937;
    --color-text-primary: #f9fafb;
    --color-text-secondary: #e5e7eb;
    --color-border: #374151;
    --color-card-bg: #1f2937;
    --color-card-shadow: rgba(0, 0, 0, 0.3);
    --color-input-bg: #374151;
    --color-input-border: #4b5563;
    --color-header-bg: #1f2937;

    /* Toast notification variables for dark mode */
    --toast-bg: #1f2937;
    --toast-color: #f9fafb;
    --toast-border: #374151;
  }

  html {
    font-family: 'Almarai', sans-serif;
    direction: rtl;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    margin: 0;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold text-secondary-900 dark:text-secondary-100;
  }

  button {
    @apply select-none cursor-pointer;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  button:active {
    transform: none !important;
  }

  button:focus {
    outline: none;
  }
}

@layer components {
  /* Primary Button */
  .btn-primary {
    @apply py-3 px-5 bg-primary-600 text-white font-semibold rounded-xl shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 transition-colors duration-200 text-base cursor-pointer;
  }

  /* Secondary Button */
  .btn-secondary {
    @apply py-3 px-5 bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-100 font-semibold rounded-xl shadow-sm hover:bg-secondary-200 dark:hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-secondary-300 dark:focus:ring-secondary-600 transition-colors duration-200 text-base cursor-pointer;
  }

  /* Success Button */
  .btn-success {
    @apply py-3 px-5 bg-success-600 text-white font-semibold rounded-xl shadow-sm hover:bg-success-700 focus:outline-none focus:ring-2 focus:ring-success-500 transition-colors duration-200 text-base cursor-pointer;
  }

  /* Danger Button */
  .btn-danger {
    @apply py-3 px-5 bg-danger-600 text-white font-semibold rounded-xl shadow-sm hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-danger-500 transition-colors duration-200 text-base cursor-pointer;
  }

  /* Icon Button */
  .btn-icon {
    @apply p-3 rounded-full bg-white dark:bg-gray-800 text-secondary-700 dark:text-secondary-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200 cursor-pointer;
  }

  /* Action Button for Dashboard */
  .btn-action {
    @apply flex items-center justify-center gap-2 bg-white dark:bg-gray-800 text-primary-700 dark:text-primary-300 rounded-xl py-3 px-4 shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200 w-full cursor-pointer;
  }

  /* Outline Button */
  .btn-outline {
    @apply py-3 px-5 bg-transparent border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-xl shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200 text-base cursor-pointer;
  }

  /* Small Button Variants */
  .btn-primary-sm {
    @apply py-2 px-3 bg-primary-600 text-white font-medium rounded-lg shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 transition-colors duration-200 text-sm cursor-pointer;
  }

  .btn-secondary-sm {
    @apply py-2 px-3 bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-100 font-medium rounded-lg shadow-sm hover:bg-secondary-200 dark:hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-secondary-300 dark:focus:ring-secondary-600 transition-colors duration-200 text-sm cursor-pointer;
  }

  .btn-success-sm {
    @apply py-2 px-3 bg-success-600 text-white font-medium rounded-lg shadow-sm hover:bg-success-700 focus:outline-none focus:ring-2 focus:ring-success-500 transition-colors duration-200 text-sm cursor-pointer;
  }

  .btn-danger-sm {
    @apply py-2 px-3 bg-danger-600 text-white font-medium rounded-lg shadow-sm hover:bg-danger-700 focus:outline-none focus:ring-2 focus:ring-danger-500 transition-colors duration-200 text-sm cursor-pointer;
  }

  .btn-warning-sm {
    @apply py-2 px-3 bg-warning-600 text-white font-medium rounded-lg shadow-sm hover:bg-warning-700 focus:outline-none focus:ring-2 focus:ring-warning-500 transition-colors duration-200 text-sm cursor-pointer;
  }

  .btn-outline-sm {
    @apply py-2 px-3 bg-transparent border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200 text-sm cursor-pointer;
  }

  /* Form Controls */
  .form-select-sm {
    @apply appearance-none rounded-md relative block px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 dark:bg-gray-700 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm transition-all duration-200 cursor-pointer;
  }

  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 cursor-pointer;
  }

  /* Action Button Icon Container */
  .btn-action-icon {
    @apply flex items-center justify-center text-primary-600 dark:text-primary-400;
  }

  /* Touch Card */
  .touch-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm p-5 transition-shadow duration-200 dark:border dark:border-gray-700 hover:shadow-md;
  }

  /* Input Field */
  .input-field {
    @apply appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-gray-100 dark:bg-gray-700 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 focus:z-10 text-sm transition-all duration-200;
  }

  /* Form Card */
  .form-card {
    @apply max-w-md w-full space-y-8 p-10 bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:border dark:border-gray-700;
  }

  /* Page Container */
  .page-container {
    @apply min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8;
  }

  /* Touch Container */
  .touch-container {
    @apply container mx-auto px-4 py-6;
  }

  /* Dashboard Item */
  .dashboard-item {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow duration-200 cursor-pointer border border-transparent hover:border-primary-100 dark:hover:border-primary-800 dark:text-gray-200;
  }

  /* Animated Icon */
  .animated-icon {
    @apply transition-transform duration-200 transform hover:scale-110;
  }

  /* Touch Table */
  .touch-table {
    @apply min-w-full rounded-xl overflow-hidden shadow-sm bg-white dark:bg-gray-800 dark:border dark:border-gray-700;
  }

  .touch-table th {
    @apply py-4 px-6 bg-gray-50 dark:bg-gray-700 text-right text-xs font-medium text-secondary-700 dark:text-secondary-200 uppercase tracking-wider;
  }

  .touch-table td {
    @apply py-4 px-6 whitespace-nowrap text-sm text-secondary-800 dark:text-secondary-200;
  }

  /* Badge */
  .badge {
    @apply px-3 py-1.5 inline-flex text-sm leading-5 font-semibold rounded-full;
  }

  .badge-success {
    @apply bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200;
  }

  .badge-warning {
    @apply bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200;
  }

  .badge-danger {
    @apply bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200;
  }

  .badge-info {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200;
  }

  /* Product card styles */
  .product-card {
    @apply relative overflow-hidden rounded-xl cursor-pointer transition-all duration-300 h-full;
  }

  .product-card-inner {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md p-3 h-full relative border border-gray-100 dark:border-gray-700 hover:border-primary-200 dark:hover:border-primary-700 transition-all duration-300 min-h-[200px] max-h-[220px];
  }

  .product-card-overlay {
    @apply absolute inset-0 bg-primary-900/70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl;
  }

  .add-to-cart-btn {
    @apply flex items-center gap-2 bg-white dark:bg-gray-800 text-primary-700 dark:text-primary-300 font-semibold py-2 px-3 rounded-lg shadow-lg transform scale-90 group-hover:scale-100 transition-all duration-300 text-sm;
  }
}

@layer utilities {
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  .touch-scale {
    @apply hover:shadow-md transition-shadow duration-200;
  }

  .no-tap-highlight {
    -webkit-tap-highlight-color: transparent;
  }

  /* Fix button interaction issues */
  .btn-fix {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -khtml-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
  }

  .btn-fix:active {
    transform: none !important;
    scale: none !important;
  }

  .btn-fix:focus {
    outline: none !important;
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .scale-102 {
    transform: scale(1.02);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Custom thin scrollbar for suggestions */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 2px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  .dark .scrollbar-thin {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: rgb(209 213 219) transparent;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
  }

  .dark .scrollbar-thumb-gray-600 {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
  }

  .scrollbar-track-gray-100 {
    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }

  .dark .scrollbar-track-gray-800 {
    scrollbar-color: rgb(75 85 99) rgb(31 41 55);
  }

  .dark .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background: rgb(31 41 55);
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  @keyframes slideDown {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes fadeInUp {
    from {
      transform: translateY(20px);
      opacity: 0;
      scale: 0.8;
    }
    to {
      transform: translateY(0);
      opacity: 1;
      scale: 1;
    }
  }

  @keyframes slideInFromBottom {
    from {
      transform: translateY(30px);
      opacity: 0;
      scale: 0.9;
    }
    to {
      transform: translateY(0);
      opacity: 1;
      scale: 1;
    }
  }

  @keyframes staggeredFadeIn {
    from {
      transform: translateY(15px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fadeOutDown {
    from {
      transform: translateY(0);
      opacity: 1;
      scale: 1;
    }
    to {
      transform: translateY(20px);
      opacity: 0;
      scale: 0.8;
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-scaleIn {
    animation: scaleIn 0.4s ease-out forwards;
  }

  .animate-slideDown {
    animation: slideDown 0.3s ease-out forwards;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease-out forwards;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-fadeOutDown {
    animation: fadeOutDown 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
  }

  .animate-slideInFromBottom {
    animation: slideInFromBottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-staggeredFadeIn {
    animation: staggeredFadeIn 0.4s ease-out forwards;
  }

  /* Staggered animation delays for smooth loading */
  .animate-staggered-1 { animation-delay: 0.1s; }
  .animate-staggered-2 { animation-delay: 0.2s; }
  .animate-staggered-3 { animation-delay: 0.3s; }
  .animate-staggered-4 { animation-delay: 0.4s; }
  .animate-staggered-5 { animation-delay: 0.5s; }
  .animate-staggered-6 { animation-delay: 0.6s; }

  /* ApexCharts Dark Mode Styles */
  .dark-chart .apexcharts-toolbar {
    background: #374151 !important;
    border: 1px solid #4B5563 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3) !important;
  }

  .dark-chart .apexcharts-toolbar .apexcharts-menu-icon,
  .dark-chart .apexcharts-toolbar .apexcharts-zoom-icon,
  .dark-chart .apexcharts-toolbar .apexcharts-reset-icon,
  .dark-chart .apexcharts-toolbar .apexcharts-pan-icon,
  .dark-chart .apexcharts-toolbar .apexcharts-download-icon {
    fill: #E5E7EB !important;
  }

  .dark-chart .apexcharts-toolbar .apexcharts-menu-icon:hover,
  .dark-chart .apexcharts-toolbar .apexcharts-zoom-icon:hover,
  .dark-chart .apexcharts-toolbar .apexcharts-reset-icon:hover,
  .dark-chart .apexcharts-toolbar .apexcharts-pan-icon:hover,
  .dark-chart .apexcharts-toolbar .apexcharts-download-icon:hover {
    fill: #F3F4F6 !important;
  }

  .dark-chart .apexcharts-menu {
    background: #374151 !important;
    border: 1px solid #4B5563 !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3) !important;
  }

  .dark-chart .apexcharts-menu-item {
    color: #E5E7EB !important;
  }

  .dark-chart .apexcharts-menu-item:hover {
    background: #4B5563 !important;
  }

  /* Light Mode Styles */
  .light-chart .apexcharts-toolbar {
    background: #ffffff !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  }

  .light-chart .apexcharts-toolbar .apexcharts-menu-icon,
  .light-chart .apexcharts-toolbar .apexcharts-zoom-icon,
  .light-chart .apexcharts-toolbar .apexcharts-reset-icon,
  .light-chart .apexcharts-toolbar .apexcharts-pan-icon,
  .light-chart .apexcharts-toolbar .apexcharts-download-icon {
    fill: #374151 !important;
  }

  .light-chart .apexcharts-toolbar .apexcharts-menu-icon:hover,
  .light-chart .apexcharts-toolbar .apexcharts-zoom-icon:hover,
  .light-chart .apexcharts-toolbar .apexcharts-reset-icon:hover,
  .light-chart .apexcharts-toolbar .apexcharts-pan-icon:hover,
  .light-chart .apexcharts-toolbar .apexcharts-download-icon:hover {
    fill: #1F2937 !important;
  }

  .light-chart .apexcharts-menu {
    background: #ffffff !important;
    border: 1px solid #E5E7EB !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  }

  .light-chart .apexcharts-menu-item {
    color: #374151 !important;
  }

  .light-chart .apexcharts-menu-item:hover {
    background: #F3F4F6 !important;
  }

  /* Custom Scrollbar Styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  /* WebKit Scrollbar Styles (Chrome, Safari, Edge) */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Dark mode scrollbar */
  .dark .custom-scrollbar {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Scrollbar for modal content */
  .modal-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175 / 0.5) transparent;
  }

  .modal-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .modal-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 8px;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(156 163 175 / 0.6);
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128 / 0.8);
  }

  /* Dark mode modal scrollbar */
  .dark .modal-scrollbar {
    scrollbar-color: rgb(75 85 99 / 0.6) transparent;
  }

  .dark .modal-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(75 85 99 / 0.6);
  }

  .dark .modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128 / 0.8);
  }

  /* Enhanced scrollbar for dropdowns */
  .dropdown-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) transparent;
  }

  /* Custom scrollbar for folder picker - Auto hide */
  .folder-picker-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
    transition: scrollbar-color 0.3s ease;
  }

  .folder-picker-scrollbar:hover,
  .folder-picker-scrollbar:focus-within,
  .folder-picker-scrollbar.scrolling {
    scrollbar-color: rgb(156 163 175 / 0.7) transparent;
  }

  .folder-picker-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .folder-picker-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 8px;
    margin: 8px 0;
  }

  .folder-picker-scrollbar::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 8px;
    border: 2px solid transparent;
    background-clip: content-box;
    transition: all 0.3s ease;
  }

  .folder-picker-scrollbar:hover::-webkit-scrollbar-thumb,
  .folder-picker-scrollbar:focus-within::-webkit-scrollbar-thumb,
  .folder-picker-scrollbar.scrolling::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgb(156 163 175 / 0.7), rgb(107 114 128 / 0.8));
  }

  .folder-picker-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgb(107 114 128 / 0.9), rgb(75 85 99 / 1)) !important;
  }

  /* Dark mode folder picker scrollbar */
  .dark .folder-picker-scrollbar {
    scrollbar-color: transparent transparent;
  }

  .dark .folder-picker-scrollbar:hover,
  .dark .folder-picker-scrollbar:focus-within,
  .dark .folder-picker-scrollbar.scrolling {
    scrollbar-color: rgb(75 85 99 / 0.7) transparent;
  }

  .dark .folder-picker-scrollbar:hover::-webkit-scrollbar-thumb,
  .dark .folder-picker-scrollbar:focus-within::-webkit-scrollbar-thumb,
  .dark .folder-picker-scrollbar.scrolling::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgb(75 85 99 / 0.7), rgb(55 65 81 / 0.8));
  }

  .dark .folder-picker-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgb(55 65 81 / 0.9), rgb(31 41 55 / 1)) !important;
  }

  .dropdown-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .dropdown-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .dropdown-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .dropdown-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  /* Dark mode dropdown scrollbar */
  .dark .dropdown-scrollbar {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .dropdown-scrollbar::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
  }

  .dark .dropdown-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Hide default number input spinners */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }

  /* Custom number input styling */
  .number-input-custom {
    -webkit-appearance: none;
    -moz-appearance: textfield;
  }

  /* Modal z-index layers */
  .z-60 {
    z-index: 60;
  }

  .z-70 {
    z-index: 70;
  }

  .number-input-custom::-webkit-outer-spin-button,
  .number-input-custom::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Navigation Cards Grid - Enhanced Responsive Design */
  .nav-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    align-items: stretch;
    width: 100%;
    padding: 0.5rem;
    margin: -0.5rem;
    overflow: visible;
    position: relative;
    z-index: 1;
  }

  /* Extra small screens (320px - 374px) */
  @media (max-width: 374px) {
    .nav-cards-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;
    }
  }

  /* Small screens (375px - 639px) */
  @media (min-width: 375px) and (max-width: 639px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
      gap: 0.75rem;
    }
  }

  /* Medium small screens (640px - 767px) */
  @media (min-width: 640px) and (max-width: 767px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 1rem;
    }
  }

  /* Tablet screens (768px - 1023px) */
  @media (min-width: 768px) and (max-width: 1023px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      max-width: 100%;
    }
  }

  /* Small desktop (1024px - 1279px) */
  @media (min-width: 1024px) and (max-width: 1279px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 0.875rem;
      max-width: 100%;
    }
  }

  /* Large desktop (1280px - 1535px) */
  @media (min-width: 1280px) and (max-width: 1535px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(145px, 1fr));
      gap: 1rem;
      max-width: none;
    }
  }

  /* Extra large desktop (1536px - 1919px) */
  @media (min-width: 1536px) and (max-width: 1919px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
      gap: 1.125rem;
    }
  }

  /* Ultra wide screens (1920px+) */
  @media (min-width: 1920px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 1.25rem;
      max-width: 1800px;
      margin: 0 auto;
    }
  }

  /* 4K and larger screens (2560px+) */
  @media (min-width: 2560px) {
    .nav-cards-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      max-width: 2000px;
      margin: 0 auto;
    }
  }

  /* Very small screens (iPhone SE and similar) */
  @media (max-width: 320px) {
    .nav-cards-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.375rem;
    }

    .nav-card {
      min-height: 85px !important;
    }
  }

  /* Enhanced card styling with better responsive behavior */
  .nav-card {
    @apply transform transition-all duration-300 ease-out;
    min-height: 120px;
    position: relative;
    overflow: visible;
    isolation: isolate;
  }

  /* Responsive card heights */
  @media (max-width: 639px) {
    .nav-card {
      min-height: 100px;
    }
  }

  @media (min-width: 640px) and (max-width: 1023px) {
    .nav-card {
      min-height: 110px;
    }
  }

  @media (min-width: 1024px) {
    .nav-card {
      min-height: 120px;
    }
  }

  /* Enhanced hover effects with device-specific optimizations */
  @media (hover: hover) and (pointer: fine) {
    .nav-card:hover {
      transform: translateY(-2px) scale(1.01);
      box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 10px -5px rgba(0, 0, 0, 0.04);
      z-index: 10;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .nav-card:active {
      transform: scale(0.98);
      transition: transform 0.1s ease-out;
    }
  }

  /* Desktop hover effects */
  @media (min-width: 1024px) and (hover: hover) {
    .nav-card:hover {
      transform: translateY(-3px) scale(1.015);
      box-shadow: 0 12px 30px -5px rgba(0, 0, 0, 0.12), 0 6px 15px -5px rgba(0, 0, 0, 0.06);
    }
  }

  /* Focus states for accessibility */
  .nav-card:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  .nav-card:focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  /* Navigation Card Content Responsive Styling */
  .nav-card-icon {
    transition: all 0.3s ease;
  }

  /* Responsive icon sizes */
  @media (max-width: 374px) {
    .nav-card-icon {
      padding: 0.5rem !important;
    }
    .nav-card-icon svg {
      font-size: 1rem !important;
    }
  }

  @media (min-width: 375px) and (max-width: 639px) {
    .nav-card-icon {
      padding: 0.625rem !important;
    }
    .nav-card-icon svg {
      font-size: 1.125rem !important;
    }
  }

  @media (min-width: 640px) and (max-width: 1023px) {
    .nav-card-icon {
      padding: 0.75rem !important;
    }
    .nav-card-icon svg {
      font-size: 1.25rem !important;
    }
  }

  @media (min-width: 1024px) {
    .nav-card-icon {
      padding: 1rem !important;
    }
    .nav-card-icon svg {
      font-size: 1.5rem !important;
    }
  }

  /* Responsive text sizing for navigation cards */
  .nav-card-title {
    transition: font-size 0.3s ease;
  }

  .nav-card-description {
    transition: font-size 0.3s ease;
    line-height: 1.4;
  }

  @media (max-width: 374px) {
    .nav-card-title {
      font-size: 0.75rem !important;
      line-height: 1.2;
      margin-bottom: 0.25rem !important;
    }
    .nav-card-description {
      font-size: 0.625rem !important;
      line-height: 1.3;
    }
  }

  @media (min-width: 375px) and (max-width: 639px) {
    .nav-card-title {
      font-size: 0.875rem !important;
      line-height: 1.25;
      margin-bottom: 0.25rem !important;
    }
    .nav-card-description {
      font-size: 0.75rem !important;
    }
  }

  @media (min-width: 640px) and (max-width: 1023px) {
    .nav-card-title {
      font-size: 0.875rem !important;
      margin-bottom: 0.375rem !important;
    }
    .nav-card-description {
      font-size: 0.75rem !important;
    }
  }

  @media (min-width: 1024px) {
    .nav-card-title {
      font-size: 1rem !important;
      margin-bottom: 0.5rem !important;
    }
    .nav-card-description {
      font-size: 0.875rem !important;
    }
  }

  /* Enhanced spacing for navigation cards */
  .nav-card-content {
    padding: 0.75rem;
  }

  @media (max-width: 374px) {
    .nav-card-content {
      padding: 0.5rem !important;
    }
  }

  @media (min-width: 375px) and (max-width: 639px) {
    .nav-card-content {
      padding: 0.625rem !important;
    }
  }

  @media (min-width: 640px) and (max-width: 1023px) {
    .nav-card-content {
      padding: 0.75rem !important;
    }
  }

  @media (min-width: 1024px) {
    .nav-card-content {
      padding: 1rem !important;
    }
  }

  /* Improved grid behavior for different card counts */
  .nav-cards-grid[data-card-count="5"] {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  }

  .nav-cards-grid[data-card-count="6"] {
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
  }

  .nav-cards-grid[data-card-count="7"] {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .nav-cards-grid[data-card-count="8"] {
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  }

  @media (max-width: 639px) {
    .nav-cards-grid[data-card-count="5"],
    .nav-cards-grid[data-card-count="6"],
    .nav-cards-grid[data-card-count="7"],
    .nav-cards-grid[data-card-count="8"] {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 640px) and (max-width: 1023px) {
    .nav-cards-grid[data-card-count="5"] {
      grid-template-columns: repeat(3, 1fr);
    }
    .nav-cards-grid[data-card-count="6"] {
      grid-template-columns: repeat(3, 1fr);
    }
    .nav-cards-grid[data-card-count="7"],
    .nav-cards-grid[data-card-count="8"] {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  /* Landscape mobile optimization for navigation cards */
  @media (max-height: 500px) and (orientation: landscape) and (max-width: 1023px) {
    .nav-cards-grid {
      gap: 0.5rem;
    }

    .nav-card {
      min-height: 80px !important;
    }

    .nav-card-content {
      padding: 0.5rem !important;
    }

    .nav-card-icon {
      padding: 0.5rem !important;
      margin-bottom: 0.25rem !important;
    }

    .nav-card-icon svg {
      font-size: 1rem !important;
    }

    .nav-card-title {
      font-size: 0.75rem !important;
      margin-bottom: 0.125rem !important;
    }

    .nav-card-description {
      font-size: 0.625rem !important;
      line-height: 1.2;
    }
  }

  /* High DPI screens optimization */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .nav-card {
      border-width: 0.5px;
    }

    .nav-card-icon {
      border-radius: 50%;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  /* Accessibility improvements for navigation cards */
  @media (prefers-reduced-motion: reduce) {
    .nav-card,
    .nav-card-icon,
    .nav-card-title,
    .nav-card-description {
      transition: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .nav-card {
      border: 2px solid;
      border-color: currentColor;
    }

    .nav-card-icon {
      border: 1px solid currentColor;
    }
  }

  /* Print styles for navigation cards */
  @media print {
    .nav-cards-grid {
      display: none;
    }
  }

  /* Dashboard Stats Cards Responsive Grid */
  .stats-grid-4 {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 640px) {
    .stats-grid-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .stats-grid-4 {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .stats-grid-5 {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 640px) {
    .stats-grid-5 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .stats-grid-5 {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1280px) {
    .stats-grid-5 {
      grid-template-columns: repeat(5, 1fr);
    }
  }

  /* Ensure stats cards have consistent height */
  .stats-card {
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  /* Container responsive improvements */
  .nav-cards-container {
    width: 100%;
    max-width: 100%;
    overflow: visible;
    padding: 0.75rem 0;
    margin: -0.75rem 0;
    position: relative;
  }

  /* Ensure parent containers don't clip hover effects */
  .nav-cards-container::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    pointer-events: none;
    z-index: -1;
  }

  /* Smooth transitions for all responsive changes */
  .nav-cards-grid,
  .nav-card,
  .nav-card-icon,
  .nav-card-title,
  .nav-card-description {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Prevent layout shift during responsive changes */
  .nav-cards-grid {
    contain: layout style;
  }

  /* Optimize for touch devices */
  @media (pointer: coarse) {
    .nav-card {
      min-height: 100px;
      touch-action: manipulation;
    }

    .nav-card-icon {
      padding: 0.75rem !important;
    }

    .nav-card-title,
    .nav-card-description {
      user-select: none;
    }
  }

  /* Optimize for precise pointing devices */
  @media (pointer: fine) {
    .nav-card:hover .nav-card-icon {
      transform: scale(1.05);
    }

    .nav-card:hover .nav-card-title {
      color: var(--primary-600);
    }
  }

  /* Prevent overflow and clipping issues */
  .nav-cards-grid {
    overflow: visible;
  }

  .nav-card {
    overflow: visible;
    will-change: transform;
  }

  /* Ensure proper stacking context for hover effects */
  .nav-card:hover {
    position: relative;
    z-index: 20;
  }

  /* Reduce hover effects on smaller screens to prevent clipping */
  @media (max-width: 768px) {
    .nav-card:hover {
      transform: translateY(-1px) scale(1.005) !important;
      box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.08), 0 2px 8px -3px rgba(0, 0, 0, 0.04) !important;
    }
  }

  /* More subtle effects on very small screens */
  @media (max-width: 480px) {
    .nav-card:hover {
      transform: translateY(-0.5px) scale(1.002) !important;
      box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.06), 0 1px 4px -2px rgba(0, 0, 0, 0.03) !important;
    }
  }

  /* Mobile Login Page Optimizations */
  .login-mobile-optimized {
    /* Ensure proper touch targets */
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Improve mobile viewport handling */
  @media (max-width: 640px) {
    .login-container {
      min-height: 100vh;
      min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
    }

    /* Optimize spacing for small screens */
    .login-card-mobile {
      margin: 0.75rem;
      padding: 1.25rem;
      max-height: calc(100vh - 1.5rem);
      max-height: calc(100dvh - 1.5rem);
      overflow-y: auto;
    }

    /* Improve touch targets */
    .mobile-touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    /* Optimize text sizes for mobile */
    .mobile-text-optimize h1 {
      font-size: 1.75rem;
      line-height: 2rem;
    }

    .mobile-text-optimize p {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }

    /* Improve button spacing on mobile */
    .mobile-button-spacing {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
      line-height: 1.5rem;
    }

    /* Optimize form spacing */
    .mobile-form-spacing {
      gap: 1rem;
    }

    /* Improve feature icons layout */
    .mobile-features-grid {
      gap: 0.75rem;
    }

    .mobile-feature-icon {
      width: 2.5rem;
      height: 2.5rem;
    }
  }

  /* Extra small screens (iPhone SE, etc.) */
  @media (max-width: 375px) {
    .login-card-mobile {
      margin: 0.5rem;
      padding: 1rem;
    }

    .mobile-text-optimize h1 {
      font-size: 1.5rem;
      line-height: 1.75rem;
    }

    .mobile-features-grid {
      gap: 0.5rem;
    }

    .mobile-feature-icon {
      width: 2.25rem;
      height: 2.25rem;
    }
  }

  /* Landscape mobile optimization */
  @media (max-height: 500px) and (orientation: landscape) {
    .login-container {
      padding: 0.5rem;
    }

    .login-card-mobile {
      margin: 0.5rem;
      padding: 1rem;
      max-height: calc(100vh - 1rem);
    }

    /* Reduce vertical spacing in landscape */
    .landscape-compact .space-y-6 > * + * {
      margin-top: 1rem;
    }

    .landscape-compact .space-y-4 > * + * {
      margin-top: 0.75rem;
    }
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Auto scrollbar for system logs */
  .custom-scrollbar-auto {
    scrollbar-width: auto;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .custom-scrollbar-auto::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar-auto::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .custom-scrollbar-auto::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 10px;
    transition: all 0.2s ease;
  }

  .custom-scrollbar-auto::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Dark mode auto scrollbar */
  .dark .custom-scrollbar-auto {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .custom-scrollbar-auto::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
  }

  .dark .custom-scrollbar-auto::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
}