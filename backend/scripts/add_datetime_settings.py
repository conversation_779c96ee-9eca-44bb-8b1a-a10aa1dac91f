#!/usr/bin/env python3
"""
سكريبت إضافة إعدادات التاريخ والوقت والمنطقة الزمنية
يضيف الإعدادات الجديدة لتنسيق التاريخ والوقت في قاعدة البيانات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from database.session import get_db
from models.setting import Setting

def add_datetime_settings():
    """
    إضافة إعدادات التاريخ والوقت والمنطقة الزمنية الجديدة
    """
    print("🕒 بدء إضافة إعدادات التاريخ والوقت...")
    
    # الحصول على جلسة قاعدة البيانات
    db = next(get_db())
    
    # قائمة الإعدادات الجديدة
    datetime_settings = [
        {
            "key": "date_format",
            "value": "dd/MM/yyyy",
            "description": "تنسيق عرض التاريخ في التطبيق (dd/MM/yyyy, MM/dd/yyyy, yyyy-MM-dd, dd-MM-yyyy, dd.MM.yyyy, arabic)"
        },
        {
            "key": "time_format", 
            "value": "24h",
            "description": "تنسيق عرض الوقت في التطبيق (24h, 12h, 12h_ar)"
        },
        {
            "key": "timezone",
            "value": "Africa/Tripoli",
            "description": "المنطقة الزمنية المستخدمة في التطبيق"
        },
        {
            "key": "date_language",
            "value": "ar",
            "description": "لغة عرض التاريخ (ar للعربية، en للإنجليزية)"
        },
        {
            "key": "week_start_day",
            "value": "saturday",
            "description": "اليوم الأول من الأسبوع (saturday, sunday, monday)"
        },
        {
            "key": "date_separator",
            "value": "/",
            "description": "الفاصل المستخدم في التاريخ (/, -, .)"
        },
        {
            "key": "time_separator",
            "value": ":",
            "description": "الفاصل المستخدم في الوقت (:, .)"
        },
        {
            "key": "show_seconds",
            "value": "false",
            "description": "إظهار الثواني في عرض الوقت (true, false)"
        },
        {
            "key": "auto_detect_timezone",
            "value": "false",
            "description": "الكشف التلقائي عن المنطقة الزمنية من المتصفح (true, false)"
        },
        {
            "key": "datetime_display_format",
            "value": "separate",
            "description": "طريقة عرض التاريخ والوقت معاً (combined, separate)"
        }
    ]
    
    try:
        added_count = 0
        updated_count = 0
        
        for setting_data in datetime_settings:
            # التحقق من وجود الإعداد مسبقاً
            existing_setting = db.query(Setting).filter(
                Setting.key == setting_data["key"]
            ).first()
            
            if existing_setting:
                # تحديث الإعداد الموجود
                existing_setting.description = setting_data["description"]
                print(f"   ✅ تم تحديث الإعداد: {setting_data['key']}")
                updated_count += 1
            else:
                # إنشاء إعداد جديد
                new_setting = Setting(
                    key=setting_data["key"],
                    value=setting_data["value"],
                    description=setting_data["description"]
                )
                db.add(new_setting)
                print(f"   ➕ تم إضافة الإعداد: {setting_data['key']} = {setting_data['value']}")
                added_count += 1
        
        # حفظ التغييرات
        db.commit()
        
        print(f"\n✅ تم بنجاح!")
        print(f"   📊 إعدادات جديدة: {added_count}")
        print(f"   🔄 إعدادات محدثة: {updated_count}")
        print(f"   📝 إجمالي الإعدادات: {len(datetime_settings)}")
        
        # عرض الإعدادات المضافة
        print(f"\n📋 قائمة إعدادات التاريخ والوقت:")
        for setting_data in datetime_settings:
            print(f"   • {setting_data['key']}: {setting_data['value']}")
        
        print(f"\n💡 يمكنك الآن استخدام هذه الإعدادات في:")
        print(f"   - صفحة الإعدادات (تبويب إعدادات النظام)")
        print(f"   - خدمة dateTimeService")
        print(f"   - جميع أجزاء التطبيق التي تعرض التاريخ والوقت")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة إعدادات التاريخ والوقت: {str(e)}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True

def verify_settings():
    """
    التحقق من إضافة الإعدادات بنجاح
    """
    print(f"\n🔍 التحقق من الإعدادات المضافة...")
    
    db = next(get_db())
    
    try:
        # البحث عن إعدادات التاريخ والوقت
        datetime_keys = [
            'date_format', 'time_format', 'timezone', 'date_language',
            'week_start_day', 'date_separator', 'time_separator', 
            'show_seconds', 'auto_detect_timezone', 'datetime_display_format'
        ]
        
        found_settings = db.query(Setting).filter(
            Setting.key.in_(datetime_keys)
        ).all()
        
        print(f"   📊 تم العثور على {len(found_settings)} إعداد من أصل {len(datetime_keys)}")
        
        for setting in found_settings:
            print(f"   ✅ {setting.key}: {setting.value}")
        
        missing_keys = set(datetime_keys) - {s.key for s in found_settings}
        if missing_keys:
            print(f"   ⚠️  إعدادات مفقودة: {', '.join(missing_keys)}")
            return False
        
        print(f"   🎉 جميع الإعدادات موجودة ومتاحة!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في التحقق: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("=" * 60)
    print("🕒 سكريبت إضافة إعدادات التاريخ والوقت - SmartPOS")
    print("=" * 60)
    
    # إضافة الإعدادات
    success = add_datetime_settings()
    
    if success:
        # التحقق من الإضافة
        verify_settings()
        
        print(f"\n" + "=" * 60)
        print("✅ تم إكمال العملية بنجاح!")
        print("🔄 يُنصح بإعادة تشغيل الخادم لتطبيق التغييرات")
        print("=" * 60)
    else:
        print(f"\n" + "=" * 60)
        print("❌ فشلت العملية!")
        print("=" * 60)
        sys.exit(1)
